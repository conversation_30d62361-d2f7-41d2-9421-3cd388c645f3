<?php

use App\Http\Controllers\AnswerController;
use App\Http\Controllers\AppointmentController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\ProfessionalController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\MovementController;
use App\Http\Controllers\NotificationsController;
use App\Http\Controllers\PlanController;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\RecurrentPauseController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AnnotationController;
use App\Http\Controllers\AdminAnalyticsController;
use App\Http\Controllers\BugReportController;
use App\Http\Controllers\SuperAdminAuthController;
use App\Http\Middleware\AdminMiddleware;
use App\Http\Middleware\SuperAdminMiddleware;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;


// Public routes that don't require any middleware
Route::get('/get-all/psychologists', [ProfessionalController::class, 'getPublicPsychologists']);
Route::get('/get-all/psychologists/{id}', [ProfessionalController::class, 'getPublicPsychologistDetails']);

Route::group(['middleware' => 'guest'], function () {
    Route::post('/register', [AuthController::class, 'registerUser'])->name('register');
    Route::get('/login', [AuthController::class, 'login'])->name('login');
    Route::post('/login', [AuthController::class, 'loginUser'])->name('login');
    Route::post('/login-professional', [AuthController::class, 'loginAdmin'])->name('login.professional');
    Route::post('/login-super-admin', [SuperAdminAuthController::class, 'login'])->name('login.super_admin');
    Route::post('/create-company', [CompanyController::class, 'startSetup'])->name('start');
    // Route::post('/update-time', [CompanyController::class, 'updateTime']);
    Route::resource('/services', ServiceController::class);
    Route::get('/{slug}/slots', [AppointmentController::class, 'getAvailableSlots']);
    Route::get('/{slug}/manifest', [CompanyController::class, 'getManifest']);
    Route::post('/decrypt-user-data', [AuthController::class, 'decryptUserData']);
    Route::post('/complete-registration', [AuthController::class, 'completeRegistration']);
    Route::get('/{slug}/company', [CompanyController::class, 'getCompany']);
    Route::get('/{slug}/services', [ServiceController::class, 'getCompanyServices']);
    Route::get('/{slug}/professionals', [ProfessionalController::class, 'getProfessionals']);
    Route::get('/professionals', [ProfessionalController::class, 'getAllProfessionals']);
    Route::post('/forgot-password', function (Request $request) {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT ? $status : '';
        // ? back()->with(['status' => __($status)])
        // : back()->withErrors(['email' => __($status)]);
    })->name('password.email');
    Route::post('/reset-password', function (Request $request) {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password)
                ])->setRememberToken(Str::random(60));

                $user->save();

                event(new PasswordReset($user));
            }
        );
        return $status === Password::PASSWORD_RESET ? response()->json(true, 200) : response()->json('', 500);
        // ? redirect()->route('login')->with('status', __($status))
        // : back()->withErrors(['email' => [__($status)]]);
    })->name('password.update');
    Route::get('/{slug}/most-scheduled', [ServiceController::class, 'getMostScheduled']);
    Route::get('/{slug}/questions', [QuestionController::class, 'getQuestionsBySlug']);
    Route::get('/check-user', [AuthController::class, 'checkUser']);
    Route::get('/plans', [PlanController::class, 'getPlans']);
});

Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::get('/finished-form', [ClientController::class, 'userHasCompletedForm']);
    Route::get('/form', [ClientController::class, 'getForm']);
    Route::post('/answers', [AnswerController::class, 'store']);
    Route::get('/my-appointments', [AppointmentController::class, 'getClientAppointments']);
    Route::get('/orders/finished', [OrderController::class, 'getAllFinished']);
    Route::post('/orders', [OrderController::class, 'store']);
    Route::get('/orders', [OrderController::class, 'getMyOrdersAsClient'])->name('get_order');
    Route::post('/update-photo', [UserController::class, 'updatePhoto'])->name('updatePhoto');
    Route::get('/profile', function (Request $request) {
        return $request->user();
    });
    Route::get('/slots', [AppointmentController::class, 'getMySlots']);
    Route::put('/profile', function (Request $request) {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'string|email|max:255',
            'phone' => 'required|string|max:255',
        ]);
        $request->only('name', 'email', 'phone');
        $userId = Auth::id();
        $user = User::find($userId);
        $user->name = $request->name;
        if(isset($request->email)) $user->email = $request->email;
        $user->phone = $request->phone;
        $user->save();
        return $user;
    });
    Route::post('/finish-my-order/{id}', [OrderController::class, 'finishMyOrder']);
    Route::post('/finish-my-appointment/{id}', [OrderController::class, 'finishMyAppointment']);

});

Route::group(['middleware' => ['auth:admin', AdminMiddleware::class]], function () {
    Route::resource('/questions', QuestionController::class);
    Route::get('/my-questions', [QuestionController::class, 'getQuestionsByProfessional']);
    Route::resource('/annotations', AnnotationController::class);
    // Route::resource('/answers', AnswerController::class);
    Route::post('/user-answers', [AnswerController::class, 'getAnswersByCompanyUser']);
    Route::get('/notifications', [NotificationsController::class, 'getNotifications']);
    Route::post('/update-time', [CompanyController::class, 'updateTime']);
    Route::post('/update-logo', [CompanyController::class, 'updateLogo'])->name('updateLogo');
    Route::post('/update-banner', [CompanyController::class, 'updateBanner'])->name('updateBanner');
    Route::get('/my-schedule', [AppointmentController::class, 'getMyAppointments']);
    Route::get('/search-user', [UserController::class, 'searchUserByPhone']);
    Route::post('/create-client', [ClientController::class, 'createUserAndClient']);
    Route::post('/vinculate-client', [ClientController::class, 'vinculateExistingUserAsClient']);
    Route::get('/my-company', [CompanyController::class, 'getMyCompany']);
    Route::put('/my-company', [CompanyController::class, 'updateMyCompany']);
    Route::get('/users', [UserController::class, 'getAllUsers']);
    Route::get('/company-orders', [OrderController::class, 'getCompanyOrders'])->name('get_company_order');
    Route::get('/clients', [ClientController::class, 'getCustomers']);
    Route::get('/client/{id}', [ClientController::class, 'getCustomer']);
    Route::resource('/professionals', ProfessionalController::class);
    Route::post('/create-professional', [ProfessionalController::class, 'createUserAndProfessional']);
    Route::post('/vinculate-professional', [ProfessionalController::class, 'vinculateExistingUserAsProfessional']);
    Route::put('/professional/{id}', [ProfessionalController::class, 'updateProfessional']);
    Route::post('/finish-professional-bond/{id}', [ProfessionalController::class, 'finishProfessionalBond']);
    Route::post('/finish-order/{id}', [OrderController::class, 'finishOrder']);
    Route::post('/finish-appointment/{id}', [OrderController::class, 'finishAppointment']);
    Route::post('/bill-movements', [MovementController::class, 'storeBillMovement']);
    Route::post('/service-movements', [MovementController::class, 'storeServiceMovement']);
    Route::post('/block-time', [AppointmentController::class, 'blockTime']);
    Route::post('/appointments', [AppointmentController::class, 'create']);
    Route::delete('/movements/{id}', [MovementController::class, 'destroy']);
    Route::get('/movements', [MovementController::class, 'index']);
    Route::get('/movements/{id}', [MovementController::class, 'show']);
    Route::get('/movements/bill/{id}', [MovementController::class, 'getBillMovements']);
    Route::get('/movements/service/{id}', [MovementController::class, 'getServiceMovements']);
    Route::get('/movements/movements', [MovementController::class, 'index']);
    Route::get('/movements/bill-movements', [MovementController::class, 'index']);
    Route::get('/movements/service-movements', [MovementController::class, 'index']);
    Route::get('/movements/bill-movements/{id}', [MovementController::class, 'getBillMovements']);
    Route::get('/movements/service-movements/{id}', [MovementController::class, 'getServiceMovements']);
    Route::post('/bill-movements', [MovementController::class, 'storeBillMovement']);
    Route::post('/service-movements', [MovementController::class, 'storeServiceMovement']);
    Route::put('/movements/{id}', [MovementController::class, 'update']);

    // Recurrent Pauses
    Route::get('/recurrent-pauses', [RecurrentPauseController::class, 'index']);
    Route::post('/recurrent-pauses', [RecurrentPauseController::class, 'store']);
    Route::put('/recurrent-pauses/{recurrentPause}', [RecurrentPauseController::class, 'update']);
    Route::delete('/recurrent-pauses/{recurrentPause}', [RecurrentPauseController::class, 'destroy']);
    Route::post('/recurrent-pauses/{recurrentPause}/toggle', [RecurrentPauseController::class, 'toggle']);
});

// Super Admin Routes
Route::group(['prefix' => 'super-admin', 'middleware' => ['auth:sanctum', SuperAdminMiddleware::class]], function () {
    // Authentication
    Route::post('/logout', [SuperAdminAuthController::class, 'logout']);
    Route::get('/user', [SuperAdminAuthController::class, 'user']);
    Route::put('/profile', [SuperAdminAuthController::class, 'updateProfile']);

    // Super Admin Management
    Route::get('/admins', [SuperAdminAuthController::class, 'getSuperAdmins']);
    Route::post('/admins', [SuperAdminAuthController::class, 'createSuperAdmin']);
    Route::delete('/admins/{user}', [SuperAdminAuthController::class, 'deleteSuperAdmin']);

    // Analytics Dashboard
    Route::get('/analytics/overview', [AdminAnalyticsController::class, 'getDashboardOverview']);
    Route::get('/analytics/users', [AdminAnalyticsController::class, 'getUserAnalytics']);
    Route::get('/analytics/appointments', [AdminAnalyticsController::class, 'getAppointmentAnalytics']);
    Route::get('/analytics/revenue', [AdminAnalyticsController::class, 'getRevenueAnalytics']);
    Route::get('/analytics/access', [AdminAnalyticsController::class, 'getAccessAnalytics']);
    Route::get('/analytics/bugs', [AdminAnalyticsController::class, 'getBugReportsAnalytics']);
    Route::get('/analytics/google', [AdminAnalyticsController::class, 'getGoogleAnalytics']);

    // Bug Reports Management
    Route::get('/bug-reports', [BugReportController::class, 'index']);
    Route::get('/bug-reports/statistics', [BugReportController::class, 'statistics']);
    Route::get('/bug-reports/categories', [BugReportController::class, 'categories']);
    Route::get('/bug-reports/{bugReport}', [BugReportController::class, 'show']);
    Route::put('/bug-reports/{bugReport}', [BugReportController::class, 'update']);
    Route::delete('/bug-reports/{bugReport}', [BugReportController::class, 'destroy']);
    Route::post('/bug-reports/{bugReport}/assign', [BugReportController::class, 'assign']);
    Route::post('/bug-reports/{bugReport}/resolve', [BugReportController::class, 'resolve']);
});

// Bug Reports (accessible to all authenticated users)
Route::group(['middleware' => 'auth:sanctum'], function () {
    Route::post('/bug-reports', [BugReportController::class, 'store']);
});

// Route::apiResource('annotations', AnnotationController::class);

<?php

namespace App\Http\Controllers;

use App\Models\Appointment;
use App\Models\AssociatedProduct;
use App\Models\Company;
use App\Models\CompanyProfessional;
use App\Models\CompanyUser;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\ProductVariant;
use Illuminate\Support\Facades\Log;

use App\Models\Service;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class AppointmentController extends Controller
{
    private function getCompanyHours(Company $company, Carbon $date)
    {
        $dayOfWeek = strtolower($date->format('l')); // gets day name in lowercase
        $timeField = "{$dayOfWeek}_time";
        $pauseField = "{$dayOfWeek}_pause";

        $workingHours = $company->{$timeField};
        $pauseHours = $company->{$pauseField};

        if (empty($workingHours)) {
            return null;
        }

        list($workStart, $workEnd) = explode('-', $workingHours);
        $pauseTimes = !empty($pauseHours) ? explode('-', $pauseHours) : null;

        return [
            'start' => Carbon::parse($date->format('Y-m-d') . ' ' . $workStart),
            'end' => Carbon::parse($date->format('Y-m-d') . ' ' . $workEnd),
            'pauseStart' => $pauseTimes ? Carbon::parse($date->format('Y-m-d') . ' ' . $pauseTimes[0]) : null,
            'pauseEnd' => $pauseTimes ? Carbon::parse($date->format('Y-m-d') . ' ' . $pauseTimes[1]) : null,
        ];
    }

    public function getAvailableSlots(Request $request, string $slug)
    {
        $company = Company::where('slug', $slug)->firstOrFail();
        $professionalId = $request->query('professionalId');

        $professionals = CompanyProfessional::where('company_id', $company->id)->get();
        if ($professionals->count() === 1) {
            $professionalId = $professionals[0]->id;
        }

        $date = $request->query('date');
        $requestedDate = Carbon::parse($date);

        // Check if the requested date is within the next 3 months
        $threeMonthsFromNow = Carbon::now()->addMonths(3);
        if ($requestedDate->gt($threeMonthsFromNow)) {
            return response()->json(['message' => 'Appointments can only be scheduled within the next 3 months'], 400);
        }

        $serviceId = $request->query('serviceId');
        $userId = Auth::id();
        $currentProfessional = $professionalId ?? $userId;

        $service = Service::findOrFail($serviceId);
        $duration = $service->duration; // Duration in minutes
        $workHours = $this->getCompanyHours($company, Carbon::parse($date));

        if (!$workHours) {
            return response()->json(['message' => 'Company is closed on this day'], 400);
        }

        $workStart = $workHours['start'];
        $workEnd = $workHours['end'];
        $now = Carbon::now();

        $appointments = Appointment::where('company_professional_id', $currentProfessional)
            ->whereBetween('start_date', [$workStart, $workEnd])
            ->where('status', 'active')
            ->orWhere('status', 'blocked')
            ->get();



        $slots = [];
        $currentSlot = $workStart;

        while ($currentSlot->lt($workEnd)) {
            $nextSlot = $currentSlot->copy()->addMinutes($duration);

            $isInPast = $currentSlot->lt($now);
            $isInPauseTime = false;

            // Check if slot is during pause time
            if ($workHours['pauseStart'] && $workHours['pauseEnd']) {
                if ($currentSlot->lt($workHours['pauseEnd']) && $nextSlot->gt($workHours['pauseStart'])) {
                    $currentSlot = $workHours['pauseEnd'];
                    continue;
                }
            }

            $isOccupied = false;

            // Check regular appointments
            foreach ($appointments as $appointment) {
                $appointmentStart = Carbon::parse($appointment->start_date);
                $appointmentEnd = Carbon::parse($appointment->end_date);

                // Check if there's any overlap between current slot and appointment
                if ($currentSlot->lt($appointmentEnd) && $nextSlot->gt($appointmentStart)) {
                    $isOccupied = true;
                    // Set next slot to start right after this appointment ends
                    $currentSlot = $appointmentEnd;
                    break;
                }
            }



            if (!$isOccupied) {
                $slots[] = [
                    'hour' => $currentSlot->format('H:i'),
                    'locked' => $isInPast,
                ];
                $currentSlot = $currentSlot->copy()->addMinutes($duration);
            }
        }

        return response()->json($slots, 200);
    }
    public function getMySlots(Request $request)
    {
        $currentProfessionalId = Auth::id();
        $company = Company::findOrFail($request->get('company_id'));
        $professional = CompanyProfessional::where('user_id', $currentProfessionalId)->first();
        $professionalId = null;
        if ($professional->role === 'attendant') {
            $professionalId = $request->query('professionalId');
        } else {
            $professionalId = $currentProfessionalId;
        }
        $date = Carbon::parse($request->query('date'));

        $workHours = $this->getCompanyHours($company, $date);

        if (!$workHours) {
            return response()->json(['message' => 'Company is closed on this day'], 400);
        }

        $workStart = $workHours['start'];
        $workEnd = $workHours['end'];
        $now = Carbon::now();

        $appointments = Appointment::where('company_professional_id', $professionalId)
            ->whereDate('start_date', $date)
            ->get();



        $slots = [];
        $currentSlot = $workStart;

        while ($currentSlot->lt($workEnd)) {
            $slotEnd = $currentSlot->copy()->addMinutes(30);

            // Skip slots during pause time
            if ($workHours['pauseStart'] && $workHours['pauseEnd']) {
                if ($currentSlot->lt($workHours['pauseEnd']) && $slotEnd->gt($workHours['pauseStart'])) {
                    $currentSlot = $workHours['pauseEnd'];
                    continue;
                }
            }

            // Check if slot has an appointment
            $hasAppointment = $appointments->contains(function ($appointment) use ($currentSlot) {
                $appointmentStart = Carbon::parse($appointment->start_date);
                $appointmentEnd = Carbon::parse($appointment->end_date);
                return $currentSlot->between($appointmentStart, $appointmentEnd);
            });

            if (!$hasAppointment) {
                $slots[] = [
                    'time' => $currentSlot->format('H:i'),
                    'available' => true
                ];
            }

            $currentSlot->addMinutes(30);
        }

        return response()->json($slots);
    }

    public function getMyAppointments(Request $request)
    {
        $date = $request->query('date');
        $userId = Auth::id();
        $today = Carbon::parse($date)->startOfDay();
        $nextMonth = $today->copy()->addMonth()->endOfMonth();
        $professional = CompanyProfessional::where('user_id', $userId)->where('company_id', $request->get('company_id'))->first();
        $appointments = Appointment::where('company_professional_id', $professional->id)
            // ->whereBetween('start_date', [$today, $nextMonth])
            ->where('start_date', '>=', $today)->whereIn('status', ['active', 'blocked'])
            ->get();
        return response()->json($appointments, 200);
    }
    public function getClientAppointments(Request $request)
    {
        $userId = Auth::id();
        $client = CompanyUser::where('user_id', $userId)->where('company_id', $request->get('company_id'))->first();
        $appointments = Appointment::where('company_user_id', $client->id)
            ->get();
        return response()->json($appointments, 200);
    }
    public function create(Request $request)
    {
        $data = $request->only([
            'professionalId',
            'clientId',
            'serviceId',
            'startTime',
            'date',
            'isRecurrent',
            'recursiveValue',
        ]);

        $professionalId = $data['professionalId'];
        if (!$professionalId) {
            $professionalId = CompanyProfessional::where('user_id', Auth::id())->where('company_id', $request->get('company_id'))->first()->id;
        }
        $order = Order::create([
            'company_user_id' => $data['clientId'],
            'discount' => 0,
            'total' => 0,
            'chat_id' => null,
            'company_professional_id' => $professionalId,
        ]);

        $appointment = $this->registerAppointment(array_merge($data, [
            'professionalId' => $professionalId,
            'orderId' => $order->id,
        ]));

        return response()->json($appointment, 201);
    }

    public function registerAppointment(array $data)
    {
        $company = Company::findOrFail($data['company_id']);
        $timezone = $company->timezone;

        $service = Service::findOrFail($data['serviceId']);
        $duration = $service->duration;

        // Parse input date/time in company's timezone
        $startTime = Carbon::parse("{$data['date']} {$data['startTime']}", $timezone);
        $endTime = $startTime->copy()->addMinutes($duration);

        // Validate working hours in company's timezone
        $workHours = $this->getCompanyHours($company, $startTime);
        if (!$workHours) {
            abort(400, 'Company is closed on this day');
        }

        if ($startTime->lt($workHours['start']) || $endTime->gt($workHours['end'])) {
            abort(400, 'Appointment time is outside working hours');
        }

        // Check break time overlap
        if (
            $workHours['pauseStart'] && $workHours['pauseEnd'] &&
            $startTime->lt($workHours['pauseEnd']) && $endTime->gt($workHours['pauseStart'])
        ) {
            abort(400, 'Appointment overlaps with break time');
        }

        // Check conflicts in UTC
        $utcStart = $startTime->utc();
        $utcEnd = $endTime->utc();

        $conflictingAppointments = Appointment::where('company_professional_id', $data['professionalId'])
            ->where(function ($query) use ($utcStart, $utcEnd) {
                $query->whereBetween('start_date', [$utcStart, $utcEnd])
                    ->orWhereBetween('end_date', [$utcStart, $utcEnd])
                    ->orWhere(function ($q) use ($utcStart, $utcEnd) {
                        $q->where('start_date', '<', $utcStart)
                            ->where('end_date', '>', $utcEnd);
                    });
            })
            ->whereIn('status', ['active', 'blocked'])
            ->exists();

        if ($conflictingAppointments) {
            abort(400, 'Horário já ocupado');
        }

        // Create main appointment
        $appointment = Appointment::create([
            'company_professional_id' => $data['professionalId'],
            'service_id' => $data['serviceId'],
            'order_id' => $data['orderId'],
            'start_date' => $utcStart,
            'end_date' => $utcEnd,
            'title' => $service->name,
            'status' => 'active',
            'company_user_id' => $data['clientId'],
            'client_name' => $data['clientName'],
            'is_recurrent' => $data['isRecurrent'] ?? false,
            'recursive_value' => ($data['isRecurrent'] ?? false) ? ($data['recursiveValue'] ?? 'weekly') : null,
        ]);

        // Handle recursive appointments
        if ($data['isRecurrent'] ?? false) {
            $this->createRecursiveAppointments(
                $appointment,
                $data,
                $startTime->copy(),
                $endTime->copy(),
                $timezone
            );
        }

        // Update order total
        $order = Order::findOrFail($data['orderId']);
        $order->total += $service->price;
        $order->save();

        return $appointment->load('service');
    }

    private function createRecursiveAppointments(
        Appointment $originalAppointment,
        array $data,
        Carbon $originalStart,
        Carbon $originalEnd,
        string $timezone
    ) {
        $recursiveValue = $data['recursiveValue'] ?? 'weekly';
        $duration = $originalEnd->diffInMinutes($originalStart);

        $currentDate = $originalStart->copy();
        $threeMonthsLater = Carbon::now($timezone)->addMonths(3);

        while ($currentDate->lte($threeMonthsLater)) {
            $currentDate = $this->getNextRecursiveDate($currentDate, $recursiveValue);

            // Skip original appointment date
            if ($currentDate->eq($originalStart)) {
                continue;
            }

            // Set appointment time in company timezone
            $appointmentStart = $currentDate->copy()
                ->setTime($originalStart->hour, $originalStart->minute);
            $appointmentEnd = $appointmentStart->copy()->addMinutes($duration);

            // Convert to UTC for storage
            $utcStart = $appointmentStart->utc();
            $utcEnd = $appointmentEnd->utc();

            // Validate working hours for new date
            $workHours = $this->getCompanyHours(
                Company::find($data['company_id']),
                $appointmentStart
            );

            if (
                !$workHours ||
                $appointmentStart->lt($workHours['start']) ||
                $appointmentEnd->gt($workHours['end']) ||
                ($workHours['pauseStart'] && $workHours['pauseEnd'] &&
                    $appointmentStart->lt($workHours['pauseEnd']) &&
                    $appointmentEnd->gt($workHours['pauseStart']))
            ) {
                continue;
            }

            if ($this->isTimeSlotAvailable($utcStart, $utcEnd, $data['professionalId'])) {
                $order = Order::create([
                    'company_user_id' => $data['clientId'],
                    'discount' => 0,
                    'total' => 0,
                    'chat_id' => null,
                    'company_professional_id' => $data['professionalId'],
                ]);

                Appointment::create([
                    'company_professional_id' => $data['professionalId'],
                    'service_id' => $data['serviceId'],
                    'order_id' => $order->id,
                    'start_date' => $utcStart,
                    'end_date' => $utcEnd,
                    'title' => $originalAppointment->title,
                    'status' => 'active',
                    'company_user_id' => $data['clientId'],
                    'client_name' => $data['clientName'],
                    'is_recurrent' => true,
                    'recursive_value' => $recursiveValue,
                ]);

                $order->total += Service::find($data['serviceId'])->price;
                $order->save();
            }
        }
    }


    // Ensure this function respects timezone when adding intervals
    private function getNextRecursiveDate(Carbon $date, string $recursiveValue, int $interval): Carbon
    {
        return match ($recursiveValue) {
            'weekly' => $date->addWeeks($interval),
            'biweekly' => $date->addWeeks($interval * 2),
            'monthly' => $date->addMonth(), // Handles month boundaries correctly
            default => $date->addWeek(),
        };
    }

    // private function getNextRecursiveDate(Carbon $currentDate, string $recursiveValue, int $interval): Carbon
    // {
    //     return match($recursiveValue) {
    //         'weekly' => $currentDate->addWeeks($interval),
    //         'biweekly' => $currentDate->addWeeks($interval),
    //         'monthly' => $currentDate->addMonths($interval),
    //         default => $currentDate->addWeeks(1)
    //     };
    // }

    private function isTimeSlotAvailable(Carbon $startTime, Carbon $endTime, int $professionalId): bool
    {
        $company = Company::findOrFail(request()->get('company_id'));
        $workHours = $this->getCompanyHours($company, $startTime);

        // Check if company is open on this day
        if (!$workHours) {
            return false;
        }

        // Check if appointment is within working hours
        if ($startTime->lt($workHours['start']) || $endTime->gt($workHours['end'])) {
            return false;
        }

        // Check if appointment overlaps with pause time
        if ($workHours['pauseStart'] && $workHours['pauseEnd']) {
            if ($startTime->lt($workHours['pauseEnd']) && $endTime->gt($workHours['pauseStart'])) {
                return false;
            }
        }

        // Check for conflicting appointments
        $conflictingAppointments = Appointment::where('company_professional_id', $professionalId)
            ->where('status', 'active')
            ->orWhere('status', 'blocked')
            ->where(function ($query) use ($startTime, $endTime) {
                $query->whereBetween('start_date', [$startTime, $endTime])
                    ->orWhereBetween('end_date', [$startTime, $endTime])
                    ->orWhere(function ($q) use ($startTime, $endTime) {
                        $q->where('start_date', '<=', $startTime)
                            ->where('end_date', '>=', $endTime);
                    });
            })
            ->exists();

        return !$conflictingAppointments;
    }

    public function blockTime(Request $request)
    {
        $data = $request->only(['date', 'start_time', 'end_time']);
        $startDate = Carbon::parse($data['date'] . ' ' . $data['start_time']);
        $endTime = Carbon::parse($data['date'] . ' ' . $data['end_time']);
        $professionalId = null;
        if ($request->get('professionalId')) {
            $professionalId = $request->get('professionalId');
        } else {
            $userId = Auth::id();
            $professional = CompanyProfessional::where('user_id', $userId)->where('company_id', $request->get('company_id'))->first();
            $professionalId = $professional->id;
        }
        $appointment = Appointment::create([
            'company_professional_id' => $professionalId,
            'start_date' => $startDate,
            'end_date' => $endTime,
            'status' => 'blocked',
            'order_id' => null,
            'company_user_id' => null,
            'service_id' => null,
            'title' => 'Horário bloqueado',
            'company_id' => $request->get('company_id'),
        ]);
        return response()->json($appointment, 201);
    }
}

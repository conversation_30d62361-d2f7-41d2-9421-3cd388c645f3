# Recurrent Pauses Feature

## Overview

The Recurrent Pauses feature allows professionals to create fixed recurring appointments that block time slots for other patients. This is useful for patients who have a fixed weekly appointment time (e.g., every Tuesday at 8:00 AM).

## How It Works

### 1. Creating Recurrent Appointments

When creating an appointment, you can now mark it as recurrent by setting `isRecurrent: true` in the request:

```javascript
POST /api/appointments
{
  "professionalId": 1,
  "clientId": 2,
  "serviceId": 3,
  "startTime": "08:00",
  "date": "2025-01-28",
  "isRecurrent": true
}
```

This will:
- Create a regular appointment for the specified date
- Create a recurrent pause that blocks the same time slot every week on the same day

### 2. Recurrent Pause Management

#### List Recurrent Pauses
```javascript
GET /api/recurrent-pauses?professionalId=1
```

#### Create Manual Recurrent Pause
```javascript
POST /api/recurrent-pauses
{
  "day_of_week": 1,        // 0=Sunday, 1=Monday, ..., 6=Saturday
  "start_time": "08:00",
  "end_time": "08:50",
  "title": "Fixed Patient Appointment",
  "professionalId": 1
}
```

#### Update Recurrent Pause
```javascript
PUT /api/recurrent-pauses/{id}
{
  "start_time": "09:00",
  "end_time": "09:50",
  "is_active": true
}
```

#### Toggle Active/Inactive
```javascript
POST /api/recurrent-pauses/{id}/toggle
```

#### Delete Recurrent Pause
```javascript
DELETE /api/recurrent-pauses/{id}
```

### 3. Slot Availability

The slot availability system now checks:
1. Company daily pauses (existing functionality)
2. Regular appointments (existing functionality)
3. **NEW**: Recurrent pauses for the specific day of the week

When a time slot conflicts with a recurrent pause, it will be marked as unavailable.

## Database Schema

### Appointments Table
- Added `is_recurrent` boolean field (default: false)

### New Table: recurrent_pauses
- `id`: Primary key
- `company_professional_id`: Foreign key to company_professionals
- `day_of_week`: Integer (0-6, where 0=Sunday)
- `start_time`: Time field
- `end_time`: Time field
- `title`: String (default: "Horário fixo")
- `is_active`: Boolean (default: true)
- `created_from_appointment_id`: Nullable foreign key to appointments
- `created_at`, `updated_at`: Timestamps

## Use Cases

1. **Fixed Weekly Therapy Sessions**: A patient has therapy every Tuesday at 8:00 AM
2. **Recurring Medical Appointments**: A patient needs weekly check-ups at the same time
3. **Professional Blocks**: A professional wants to block certain times for administrative work

## Frontend Integration

To integrate this feature in the frontend:

1. Add a checkbox "Recurrent Appointment" when creating appointments
2. Create a management interface for recurrent pauses
3. Display recurrent pauses in the calendar view
4. Show blocked slots due to recurrent pauses differently from regular appointments

## API Endpoints Summary

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/recurrent-pauses` | List recurrent pauses |
| POST | `/api/recurrent-pauses` | Create recurrent pause |
| PUT | `/api/recurrent-pauses/{id}` | Update recurrent pause |
| DELETE | `/api/recurrent-pauses/{id}` | Delete recurrent pause |
| POST | `/api/recurrent-pauses/{id}/toggle` | Toggle active status |
| POST | `/api/appointments` | Create appointment (with isRecurrent option) |

All endpoints require admin authentication (professional access).

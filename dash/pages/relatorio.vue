<template>
  <div>
    <div class="container-table overflow-y-auto" :class="user.isWorker && ''">
      <div class="flex justify-end mb-4">
        <select
          v-model="selectedPeriod"
          @change="updateChartData"
          class="select select-bordered"
        >
          <option value="today">Hoje</option>
          <option value="week">Esta semana</option>
          <option value="month">Este Mês</option>
          <option value="3months">Últimos 3 Meses</option>
          <option value="6months">Últimos 6 Meses</option>
        </select>
      </div>
      <div
        class="flex flex-col gap-4"
        :class="selectedType === 'receptionist' && 'lg:flex-row'"
      >
        <div
          class="grid w-full gap-4"
          :class="
            (selectedType === 'admin' && 'grid-cols-2 lg:grid-cols-5') ||
            (selectedType === 'barber' && 'grid-cols-1')
          "
          v-if="selectedType === 'admin' || selectedType === 'barber'"
        >
          <charts-card
            title="Entradas"
            :value="totalEntradas"
            v-if="selectedType === 'admin'"
          />
          <charts-card
            title="Saídas"
            :negative="true"
            :value="totalSaidas"
            v-if="selectedType === 'admin'"
          />
          <charts-card
            class="col-span-1 lg:col-span-1"
            :title="lucroReal >= 0 ? 'Lucro' : 'Prejuizo'"
            :value="lucroReal"
            v-if="selectedType === 'admin'"
          />
        </div>
        <div class="flex flex-col lg:flex-row gap-4 w-full">
          <div class="w-full elevated-card bg-base-200">
            <div class="text-lg text-center font-semibold">
              Entradas e Saídas
            </div>
            <div>
              <Bar
                v-if="!loading"
                :data="dataLine"
                :options="options"
                class="!min-h-44 max-w-full"
              />
            </div>
            <div>
              <Pie
                v-if="!loading"
                :data="dataLine"
                :options="options"
                class="!min-h-44 max-w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Colors,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
} from "chart.js";
import { Bar } from "vue-chartjs";
import { useLoginStore } from "~/store/user";
import { api } from "~/server/api";
import dayjs from "dayjs";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
  Colors
);

const selectedPeriod = ref("month"); // Período padrão
const loading = ref(false);
const user = useLoginStore().userInfo;
const selectedType = ref();
const totalEntradas = ref(0);
const totalSaidas = ref(0);
const lucroReal = computed(() => totalEntradas.value - totalSaidas.value);
const entrada = ref([]);
const saida = ref([]);
const totalDays = ref(dayjs().daysInMonth());

// Função para gerar os labels do gráfico
const generateLabels = () => {
  switch (selectedPeriod.value) {
    case "today":
      return ["Hoje"];
    case "week":
      return [
        "Segunda",
        "Terça",
        "Quarta",
        "Quinta",
        "Sexta",
        "Sábado",
        "Domingo",
      ];
    case "month":
      return [...Array(totalDays.value).keys()].map((i) => (i + 1).toString());
    case "3months":
    case "6months":
      const months = [];
      const period = selectedPeriod.value === "3months" ? 3 : 6;
      for (let i = period - 1; i >= 0; i--) {
        months.push(dayjs().subtract(i, "month").format("MMM YYYY"));
      }
      return months;
    default:
      return [];
  }
};
const options = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    colors: {
      enabled: true,
    },
    legend: {
      labels: {
        color: "white",
      },
    },
    tooltip: {
      callbacks: {
        // eslint-disable-next-line
        label: function (context: any) {
          return toBrl(context.raw);
        },
        // eslint-disable-next-line
        title: function (context: any) {
          const { label } = context[0];
          return `${label}`;
        },
      },
    },
  },
};
// Função para agrupar dados por dia ou mês
const groupData = (data, period) => {
  const groupedData = Array(totalDays.value).fill(0);

  data.forEach((mov) => {
    const date = dayjs(mov.created_at);
    if (period === "today" && date.isSame(dayjs(), "day")) {
      groupedData[0] += parseFloat(mov.total || "0");
    } else if (period === "week") {
      const dayOfWeek = date.day() - 1; // Segunda = 0, Domingo = 6
      if (dayOfWeek >= 0 && dayOfWeek < 7) {
        groupedData[dayOfWeek] += parseFloat(mov.total || "0");
      }
    } else if (period === "month") {
      const dayOfMonth = date.date() - 1; // Dia do mês (0 a 30)
      if (dayOfMonth < totalDays.value) {
        groupedData[dayOfMonth] += parseFloat(mov.total || "0");
      }
    } else if (period === "3months" || period === "6months") {
      const monthIndex = dayjs().diff(date, "month");
      if (monthIndex >= 0 && monthIndex < totalDays.value) {
        groupedData[monthIndex] += parseFloat(mov.total || "0");
      }
    }
  });

  return groupedData;
};

// Dados do gráfico
const dataLine = computed(() => {
  return {
    labels: generateLabels(),
    datasets: [
      { label: "Entradas R$", backgroundColor: "#4CAF50", data: entrada.value },
      { label: "Saídas R$", backgroundColor: "#DD1B16", data: saida.value },
    ],
  };
});

// Atualiza os dados do gráfico com base no período selecionado
function updateChartData() {
  switch (selectedPeriod.value) {
    case "today":
      totalDays.value = 1;
      break;
    case "week":
      totalDays.value = 7;
      break;
    case "month":
      totalDays.value = dayjs().daysInMonth();
      break;
    case "3months":
      totalDays.value = 3;
      break;
    case "6months":
      totalDays.value = 6;
      break;
    default:
      totalDays.value = dayjs().daysInMonth();
  }

  getMovements();
}

// Busca os movimentos e agrupa os dados
async function getMovements() {
  loading.value = true;
  try {
    const { data } = await api.get("/movements");

    const entries = data.data.filter((mov) => mov.type === "profit");
    const exits = data.data.filter((mov) => {
      return (
        mov.type === "loss" &&
        (mov.category !== "product" ||
          (mov.category === "product" && mov.reason === "purchase"))
      );
    });
    entrada.value = groupData(entries, selectedPeriod.value);
    saida.value = groupData(exits, selectedPeriod.value);

    totalEntradas.value = entrada.value.reduce((sum, value) => sum + value, 0);
    totalSaidas.value = saida.value.reduce((sum, value) => sum + value, 0);
  } catch (error) {
    console.error("Erro ao obter movimentações", error);
  } finally {
    loading.value = false;
  }
}

// Inicializa o componente
onMounted(() => {
  getUserResponsibility();
  getMovements();
});

// Função para definir a responsabilidade do usuário
async function getUserResponsibility() {
  const responsibility = ["admin", "barber", "receptionist"];
  user.responsibility = "admin";
  selectedType.value = "admin";
}
</script>

<template>
  <div>
    <div class="container-table pt-1">
      <div class="container-body flex flex-col">
        <!-- Header Section -->
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div class="hidden sm:block">
            <h1 class="font-bold text-base-content flex items-center gap-2">
              <!-- <ChartBarIcon class="w-8 h-8" /> -->
              <!-- <p class="text-base-content/70 mt-1"></p> -->
            </h1>
          </div>

          <!-- Period Filter -->
          <div class="flex items-center mb-2 gap-2">
            <span class="text-sm font-medium text-base-content/70">Período:</span>
            <select v-model="selectedPeriod" @change="updateChartData"
              class="select select-bordered select-sm bg-base-100 border-base-300 focus:border-primary">
              <option value="today">Hoje</option>
              <option value="week">Esta semana</option>
              <option value="month">Este Mês</option>
              <option value="3months">Últimos 3 Meses</option>
              <option value="6months">Últimos 6 Meses</option>
            </select>
          </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-20">
          <div class="loading loading-spinner loading-lg text-primary"></div>
        </div>

        <!-- Main Content -->
        <div v-else class="space-y-6">
          <!-- Appointment Statistics Cards - Now includes Total Appointments -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Total Appointments Card -->
            <div class="card bg-base-100 shadow-lg border border-base-300">
              <div class="card-body py-3">
                <div class="flex items-center gap-3 h-full">
                  <div class="w-12 h-12 bg-info/20 rounded-full flex items-center justify-center">
                    <CalendarDaysIcon class="w-6 h-6 text-info" />
                  </div>
                  <div>
                    <h3 class="font-semibold text-base-content">Total Agendamentos</h3>
                    <p class="text-2xl font-bold text-info">{{ appointmentStats.total_appointments || 0 }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Finished Appointments Card -->
            <div class="card bg-base-100 shadow-lg border border-base-300">
              <div class="card-body py-3">
                <div class="flex items-center gap-3 h-full">
                  <div class="w-12 h-12 bg-success/20 rounded-full flex items-center justify-center">
                    <CheckCircleIcon class="w-6 h-6 text-success" />
                  </div>
                  <div>
                    <h3 class="font-semibold text-base-content">Agendamentos Finalizados</h3>
                    <p class="text-2xl font-bold text-success">{{ appointmentStats.finished_appointments || 0 }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Canceled Appointments Card -->
            <div class="card bg-base-100 shadow-lg border border-base-300">
              <div class="card-body py-3">
                <div class="flex items-center gap-3 h-full">
                  <div class="w-12 h-12 bg-error/20 rounded-full flex items-center justify-center">
                    <XCircleIcon class="w-6 h-6 text-error" />
                  </div>
                  <div>
                    <h3 class="font-semibold text-base-content">Agendamentos Cancelados</h3>
                    <p class="text-2xl font-bold text-error">{{ appointmentStats.canceled_appointments || 0 }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Active Appointments Card -->
            <div class="card bg-base-100 shadow-lg border border-base-300">
              <div class="card-body py-3">
                <div class="flex items-center gap-3 h-full">
                  <div class="w-12 h-12 bg-warning/20 rounded-full flex items-center justify-center">
                    <ClockIcon class="w-6 h-6 text-warning" />
                  </div>
                  <div>
                    <h3 class="font-semibold text-base-content">Agendamentos Ativos</h3>
                    <p class="text-2xl font-bold text-warning">{{ appointmentStats.active_appointments || 0 }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Financial Overview Cards - Only 2 cards with outlined style -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Entradas Card -->
            <div class="card card-bordered border-primary bg-base-100 shadow-lg">
              <div class="card-body py-2">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <ArrowTrendingUpIcon class="w-8 h-8 text-primary" />
                  </div>
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-base-content">Entradas</h3>
                    <p class="text-3xl font-bold text-primary">{{ toBrl(totalEntradas) }}</p>
                    <p class="text-sm text-base-content/70">{{ getPeriodDescription() }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Saídas Card -->
            <div class="card card-bordered border-error bg-base-100 shadow-lg">
              <div class="card-body py-2">
                <div class="flex items-center gap-4">
                  <div class="w-16 h-12 bg-error/10 rounded-full flex items-center justify-center">
                    <ArrowTrendingDownIcon class="w-8 h-8 text-error" />
                  </div>
                  <div class="flex-1">
                    <h3 class="text-lg font-semibold text-base-content">Saídas</h3>
                    <p class="text-2xl font-bold text-error">{{ toBrl(totalSaidas) }}</p>
                    <p class="text-sm text-base-content/70">{{ getPeriodDescription() }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>



          <!-- Charts Section -->
          <div class="grid grid-cols-1 gap-6">
            <!-- Financial Chart -->
            <div class="card bg-base-100 shadow-lg border border-base-300">
              <div class="card-body">
                <h2 class="card-title text-xl mb-4 flex items-center gap-2">
                  <ChartBarIcon class="w-6 h-6 text-primary" />
                  Entradas e Saídas
                </h2>
                <div class="h-80">
                  <Bar v-if="!loading && dataLine?.datasets?.length > 0" :data="dataLine" :options="barChartOptions"
                    class="!h-full" />
                  <div v-else-if="!loading" class="flex items-center justify-center h-full text-base-content/50">
                    <div class="text-center">
                      <ChartBarIcon class="w-16 h-16 mx-auto mb-2 text-base-content/30" />
                      <p>Nenhum dado disponível para o período selecionado</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Appointments Pie Chart - COMMENTED OUT -->
            <!--
            <div class="card bg-base-100 shadow-lg border border-base-300">
              <div class="card-body">
                <h2 class="card-title text-xl mb-4 flex items-center gap-2">
                  <ChartPieIcon class="w-6 h-6 text-primary" />
                  Status dos Agendamentos
                </h2>
                <div class="h-80">
                  <Pie v-if="!loading && appointmentPieData?.datasets?.length > 0" :data="appointmentPieData"
                    :options="pieChartOptions" class="!h-full" />
                  <div v-else-if="!loading" class="flex items-center justify-center h-full text-base-content/50">
                    <div class="text-center">
                      <ChartPieIcon class="w-16 h-16 mx-auto mb-2 text-base-content/30" />
                      <p>Nenhum agendamento no período selecionado</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            -->
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Colors,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
} from "chart.js";
import { Bar } from "vue-chartjs";
import { api } from "~/server/api";
import dayjs from "dayjs";
import {
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarDaysIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ChartBarIcon
} from "@heroicons/vue/24/outline";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement,
  Colors
);

const selectedPeriod = ref("month");
const loading = ref(false);
const totalEntradas = ref(0);
const totalSaidas = ref(0);
const entrada = ref<number[]>([]);
const saida = ref<number[]>([]);
const totalDays = ref(dayjs().daysInMonth());
const appointmentStats = ref({
  total_appointments: 0,
  finished_appointments: 0,
  canceled_appointments: 0,
  active_appointments: 0,
  appointments_by_status: {}
});

// Função para gerar os labels do gráfico
const generateLabels = () => {
  switch (selectedPeriod.value) {
    case "today":
      return ["Hoje"];
    case "week":
      return [
        "Segunda",
        "Terça",
        "Quarta",
        "Quinta",
        "Sexta",
        "Sábado",
        "Domingo",
      ];
    case "month":
      return [...Array(totalDays.value).keys()].map((i) => (i + 1).toString());
    case "3months":
    case "6months":
      const months = [];
      const period = selectedPeriod.value === "3months" ? 3 : 6;
      for (let i = period - 1; i >= 0; i--) {
        months.push(dayjs().subtract(i, "month").format("MMM YYYY"));
      }
      return months;
    default:
      return [];
  }
};
// Helper function to get period description
const getPeriodDescription = () => {
  switch (selectedPeriod.value) {
    case "today": return "hoje";
    case "week": return "nesta semana";
    case "month": return "neste mês";
    case "3months": return "nos últimos 3 meses";
    case "6months": return "nos últimos 6 meses";
    default: return "no período";
  }
};

// Chart options
const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
      labels: {
        usePointStyle: true,
        padding: 20,
      },
    },
    tooltip: {
      callbacks: {
        label: function (context: any) {
          return `${context.dataset.label}: ${toBrl(context.raw)}`;
        },
      },
    },
  },
  scales: {
    y: {
      beginAtZero: true,
      ticks: {
        callback: function (value: any) {
          return toBrl(value);
        }
      }
    }
  }
};


// Função para agrupar dados por dia ou mês
const groupData = (data, period) => {
  const groupedData = Array(totalDays.value).fill(0);

  data.forEach((mov) => {
    const date = dayjs(mov.created_at);
    if (period === "today" && date.isSame(dayjs(), "day")) {
      groupedData[0] += parseFloat(mov.total || "0");
    } else if (period === "week") {
      const dayOfWeek = date.day() - 1; // Segunda = 0, Domingo = 6
      if (dayOfWeek >= 0 && dayOfWeek < 7) {
        groupedData[dayOfWeek] += parseFloat(mov.total || "0");
      }
    } else if (period === "month") {
      const dayOfMonth = date.date() - 1; // Dia do mês (0 a 30)
      if (dayOfMonth < totalDays.value) {
        groupedData[dayOfMonth] += parseFloat(mov.total || "0");
      }
    } else if (period === "3months" || period === "6months") {
      const monthIndex = dayjs().diff(date, "month");
      if (monthIndex >= 0 && monthIndex < totalDays.value) {
        groupedData[monthIndex] += parseFloat(mov.total || "0");
      }
    }
  });

  return groupedData;
};

// Computed properties for chart data
const dataLine = computed(() => {
  return {
    labels: generateLabels(),
    datasets: [
      {
        label: "Entradas",
        backgroundColor: "#10b981",
        borderColor: "#059669",
        data: entrada.value
      },
      {
        label: "Saídas",
        backgroundColor: "#ef4444",
        borderColor: "#dc2626",
        data: saida.value
      },
    ],
  };
});



// Atualiza os dados do gráfico com base no período selecionado
async function updateChartData() {
  switch (selectedPeriod.value) {
    case "today":
      totalDays.value = 1;
      break;
    case "week":
      totalDays.value = 7;
      break;
    case "month":
      totalDays.value = dayjs().daysInMonth();
      break;
    case "3months":
      totalDays.value = 3;
      break;
    case "6months":
      totalDays.value = 6;
      break;
    default:
      totalDays.value = dayjs().daysInMonth();
  }

  await Promise.all([getMovements(), getAppointmentStatistics()]);
}

// Busca os movimentos e agrupa os dados
async function getMovements() {
  try {
    const { data } = await api.get("/movements");

    const entries = data.data.filter((mov) => mov.type === "profit");
    const exits = data.data.filter((mov) => {
      return (
        mov.type === "loss" &&
        (mov.category !== "product" ||
          (mov.category === "product" && mov.reason === "purchase"))
      );
    });
    entrada.value = groupData(entries, selectedPeriod.value);
    saida.value = groupData(exits, selectedPeriod.value);

    totalEntradas.value = entrada.value.reduce((sum, value) => sum + value, 0);
    totalSaidas.value = saida.value.reduce((sum, value) => sum + value, 0);
  } catch (error) {
    console.error("Erro ao obter movimentações", error);
  }
}

// Busca as estatísticas de agendamentos
async function getAppointmentStatistics() {
  try {
    const { data } = await api.get("/appointment-statistics", {
      params: { period: selectedPeriod.value }
    });

    appointmentStats.value = data;
  } catch (error) {
    console.error("Erro ao obter estatísticas de agendamentos", error);
  }
}

// Função principal para carregar todos os dados
async function loadAllData() {
  loading.value = true;
  try {
    await Promise.all([getMovements(), getAppointmentStatistics()]);
  } catch (error) {
    console.error("Erro ao carregar dados", error);
  } finally {
    loading.value = false;
  }
}

// Inicializa o componente
onMounted(() => {
  loadAllData();
});
</script>
